{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "look": "serve -s dist -l 8718", "===开发===": "", "dev:overseas": "PROJECT_ENV=overseas pnpm dev", "dev:production": "PROJECT_ENV=production pnpm dev", "===打包===": "", "generate:overseas": "PROJECT_ENV=overseas pnpm generate", "generate:production": "PROJECT_ENV=production pnpm generate", "postinstall": "nuxt prepare", "===部署===": "", "===部署-发布到海外测试===": "", "upload:overseas": "./build_overseas.sh", "===部署-打包正式包===": "", "upload:production": "./build_production.sh"}, "dependencies": {"@nuxt/image": "^1.9.0", "@nuxtjs/device": "3.2.4", "@nuxtjs/i18n": "npm:@nuxtjs/i18n-edge@9.2.1-29013776.4a8d7d5", "@nuxtjs/tailwindcss": "6.13.1", "@nuxtjs/toast": "^3.3.1", "@vueuse/core": "^13.0.0", "clipboard": "^2.0.11", "cross-env": "^7.0.3", "nuxt": "^3.15.4", "postcss-loader": "^8.1.1", "postcss-px-to-viewport": "^1.1.1", "postcss-px-to-viewport-8-plugin": "^1.2.5", "vue": "latest", "vue-router": "latest"}, "devDependencies": {"sass": "^1.85.1"}}