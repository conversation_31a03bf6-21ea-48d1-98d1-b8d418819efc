// Nuxt 配置文件的链接：https://nuxt.com/docs/api/configuration/nuxt-config
let basePath = '/'
const env_PROJECT_ENV = process.env.PROJECT_ENV
console.log("🚀 ~ env_PROJECT_ENV:", env_PROJECT_ENV)
if (env_PROJECT_ENV === 'overseas') {
  basePath = '/licowa_official_website/'
} else if (env_PROJECT_ENV === 'production') {
  basePath = '/'
}
else {
  console.error('PROJECT_ENV 环境变量错误' + env_PROJECT_ENV)
}

export default defineNuxtConfig({
  app: {
    baseURL: `${basePath}`, // 应用的基础 URL
    head: {
      // title: 'Mico Widget', // 这里如果写死，会导致在切换语言时，标题不会变化
      // meta: [],
      // link: [
      //   {
      //     rel: 'icon',
      //     type: 'image/x-icon',
      //     href: `${basePath}assets/img/favicon.ico`
      //   }
      // ]
      // 禁止用户双击放大
      meta: [
        {
          name: 'viewport',
          content: 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no'
        }
      ]
    },
  },
  compatibilityDate: '2024-11-01', // 兼容性日期
  devtools: { 
    enabled: true // 启用开发者工具
  },
  modules: [
    '@nuxtjs/tailwindcss', // Tailwind CSS 插件，文档：https://tailwindcss.nuxtjs.org/
    '@nuxtjs/i18n', // 国际化插件，文档：https://i18n.nuxtjs.org/
    '@nuxtjs/device', // 设备检测插件
    '@nuxt/image', // 图片插件
  ],
  i18n: {
    vueI18n: '../i18n.config.ts' // 如果使用自定义路径，默认配置
  },
  vite: {
  },
  // postcss: {
  //   plugins: {
  //     'postcss-nested': {},
  //     'postcss-custom-media': {}
  //   }
  // }
  postcss: {
    plugins: {
      // https://blog.csdn.net/qq_33839972/article/details/125504735
      /* "postcss-px-to-viewport-8-plugin": {
          unitToConvert: 'px', //需要转换的单位，默认为"px"
          viewportWidth: (file) => {
            // 检查文件路径是否包含 Mobile 或 mobile
            if (file.includes('Mobile') || file.includes('mobile')) {
              return 375;
            }
            return 1920;
          }, // 视窗的宽度，对应的是我们设计稿的宽度
          // viewportHeight: 1334,//视窗的高度，根据375设备的宽度来指定，一般指定667，也可以不配置
          unitPrecision: 4, // 指定`px`转换为视窗单位值的小数位数（很多时候无法整除）
          propList: ['*'], // 能转化为vw的属性列表
          viewportUnit: 'vw', // 指定需要转换成的视窗单位，建议使用vw
          fontViewportUnit: 'vw', //字体使用的视口单位
          selectorBlackList: ['.ignore-', '.hairlines'], //指定不转换为视窗单位的类，可以自定义，可以无限添加,建议定义一至两个通用的类名
          minPixelValue: 1, // 小于或等于`1px`不转换为视窗单位，你也可以设置为你想要的值
          mediaQuery: false, // 允许在媒体查询中转换`px`
          replace: true, //是否直接更换属性值，而不添加备用属性
          exclude: [
            /node_modules/,
          ], //忽略某些文件夹下的文件或特定文件，例如 'node_modules' 下的文件
          landscape: false, //是否添加根据 landscapeWidth 生成的媒体查询条件 @media (orientation: landscape)
          landscapeUnit: 'vw', //横屏时使用的单位
          // landscapeWidth: 1134 //横屏时使用的视口宽度
        } */
    }
  },
})
