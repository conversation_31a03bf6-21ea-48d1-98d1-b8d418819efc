@use "./common.scss" as *;
.mico-mobile-website {
  background: #ffffff;
  $global-left-right-padding: 18px;
  $header-top-and-bottom-padding: 15px;
  $header-logo-width: 80px;
  $header-logo-height: 30px;
  $header-menu-width: 30px;
  $header-menu-height: 30px;
  
  // 主头部
  .mico-mobile-header {
    padding: 0 $global-left-right-padding;
    padding-top: $header-top-and-bottom-padding;
    padding-bottom: $header-top-and-bottom-padding;
    box-sizing: border-box;
  
    background: #fff;
    .mico-mobile-header__logo {
      width: $header-logo-width;
      height: $header-logo-height;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .mico-mobile-header__menu {
      width: $header-menu-width;
      height: $header-menu-height;
      img {
          width: 100%;
          height: 100%;
      }
    }
  }
  $header-size-height: calc(($header-top-and-bottom-padding*2) + $header-logo-height);
  // 头部占位
  .mico-mobile-header__size {
      height: $header-size-height;
  }
  
  // 移动端 - 菜单栏
  .mico-mobile-menus {
      position: fixed;
      top: calc($header-size-height);
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(252, 252, 252, 0.95); // 使用rgba设置透明背景
      z-index: 999;
      .mico-mobile-menus__box {
        width: 100%;
        height: 48px;
      }
      .mico-mobile-menus__item {
          height: 48px;
          line-height: 48px;
          text-align: center;
      }
      .mico-mobile-menus__item__text {
          font-family: Poppins, Poppins;
          font-weight: 500;
          font-size: 16px;
          color: #181E2A;
  
      }
      .mico-mobile-menus__item__text--active {
        border-bottom: 2px solid #5cbfc7;
      }
      // .mico-mobile-menus__item--active::after {
      //     content: "";
      //     display: block;
      //     width: 100%;
      //     height: 2px;
      //     background: #5cbfc7;
      //     border-radius: 0px 0px 0px 0px;
      // }
  }
  
  // 
  .content__mluv {
    @include VARCSS_content__mluv($device-mobile);
  }
  
  
  $introduce-container-margin-top: 15px;
  .mico-content__top {
      padding-left: $global-left-right-padding;;
      padding-right: $global-left-right-padding;;
      padding-top: $introduce-container-margin-top;
  }
  
  // 移产品介绍
  .content__introduce {
    @include VARCSS_content__introduce($device-mobile);
  }
  
   // 产品内容展示
   .content__productshow {
    @include VARCSS_content__productshow($device-mobile);
  }
  
  // 文章展示
  .content__article {
    @include VARCSS_content__article($device-mobile);
  }
  
  // 社交媒体
  .content__socialmedia {
    @include VARCSS_content__socialmedia($device-mobile);
  }
  
    // 团队介绍
  .content__team {
    @include VARCSS_content__team($device-mobile);
  }
  
    // 主底部
    .mico-footer {
      @include VARCSS_mico-footer($device-mobile);
    }
  
  
  .content__switchteam {
    padding: 6px 0;
    margin-bottom: 8px;
    .content__switchteam__text {
      font-family: Poppins, Poppins;
      font-weight: 400;
      font-size: 11px;
      color: #181E2A;
      line-height: 13px;
    }
    .content__switchteam__icon {
      margin-left: 2px;
      width: 24px;
      height: 24px;
    }
  }
}





.toast--mobile {
  padding: 12px 18px;
  border-radius: 8px;
  font-size: 12px;
}