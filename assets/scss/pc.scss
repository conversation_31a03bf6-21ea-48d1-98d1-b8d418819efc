@use "./common.scss" as *;
// https://nuxt.com/docs/getting-started/styling
// 主网站
.mico-pc-website {
  background: #ffffff;
  // 主容器
  .mico-pc-container {
  }
  // 主头部
  .mico-header {
    padding: 0 310px;
    padding-top: 21px;
    box-sizing: border-box;

    background: #fff;
    .mico-header__logo {
      width: 128px;
      height: 48px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .mico-header__menu__item {
      // margin-left: 48px;
      padding-left: 24px;
      padding-right: 24px;

      // height: 26px;
      // line-height: 26px;
      height: 48px;
      // background-color: red;

      font-family: Poppins-Medium, Poppins-Medium;
      font-weight: 600;
      font-size: 24px;
      color: #181e2a;
      // position: relative; // 为了宽度是文字的大小才用定位的
    }
    .mico-header__menu__item--active::after {
      content: "";
      display: block;
      width: 100%;
      height: 2px;
      background: #5cbfc7;
      border-radius: 0px 0px 0px 0px;
      // position: absolute;
      // left: 0;
      // bottom: -2px;
    }
  }

  // 头部大小
  .mico-header__size {
    height: calc(48px + 21px);
  }
  
  // 主内容
  .mico-content {
    // background: #537553;
    .mico-content__top {
      padding-left: 310px;
      padding-right: 310px;
      padding-top: 90px;
    }
    .content__top__left {
    }
    .content__top__right {
    }

    // 大 LOGO
    .content__biglogo {
      width: 232px;
      height: 232px;
      border-radius: 62px 62px 62px 62px;
      border: 2px solid rgba(24, 30, 42, 0.1);
      .content__biglogo__image {
        width: 140.47px;
        height: 140.47px;
      }
    }
    .content__mluv {
      @include VARCSS_content__mluv($device-pc);
    }

    // 介绍区域
    .content__introduce {
      @include VARCSS_content__introduce($device-pc);
    }

    // 产品内容展示
    .content__productshow {
      @include VARCSS_content__productshow($device-pc);
    }
    //
    .content__article {
      @include VARCSS_content__article($device-pc);
    }

    // 社交媒体
    .content__socialmedia {
      @include VARCSS_content__socialmedia($device-pc);
    }

    // 团队介绍
    .content__team {
      @include VARCSS_content__team($device-pc);
    }
  }
  // 主底部
  .mico-footer {
    @include VARCSS_mico-footer($device-pc);
  }
}
.toast--pc {
  padding: 16px 24px;
  border-radius: 8px;
  font-size: 14px;
}