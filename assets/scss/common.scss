// 引入字体
@font-face {
  font-family: <PERSON>, <PERSON>;
  src: url("../assets/font/BarlowCondensed-Light.otf");
}
@font-face {
  font-family: Poppins, Poppins;
  src: url("../assets/font/Poppins-Regular.ttf");
}
@font-face {
  font-family: Poppins-Medium, Poppins-Medium;
  src: url("../assets/font/Poppins-Medium.ttf");
}

$device-pc: "pc";
$device-mobile: "mobile";

// 创建一个函数来根据设备类型返回对应的值
@function deviceValue($device, $pc-value, $mobile-value) {
  @if $device == "pc" {
    @return $pc-value;
  } @else {
    @return $mobile-value;
  }
}

// 通用的伪元素样式
@mixin common-before-style($device) {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: deviceValue($device, 2px, 1px); // 特定的padding值
  border-radius: deviceValue($device, 48px, 18px);
  background: rgba(24, 30, 42, 0.12); // 普通状态下的背景颜色
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: destination-out;
  mask-composite: exclude;
}

@mixin common-after-style($paddingSize: 6px) {
  // padding: 6px; // 特定的padding值
  padding: $paddingSize; // 特定的padding值
  background: linear-gradient(
    225deg,
    rgba(147, 255, 178, 1),
    rgba(132, 234, 255, 1)
  ); // 悬停时的背景颜色
}

// 麦穗展示区域
@mixin VARCSS_content__mluv($device: "pc") {
  // 应用变量到样式
  margin-top: deviceValue($device, 73px, 12px);
  
  .content__mluv__left,
  .mluv__right {
    width: deviceValue($device, 62px, 26px);
    height: deviceValue($device, 132px, 54px);
    img {
      width: 100%;
      height: 100%;
    }
  }
  .mluv__right {
    margin-left: 12px;
  }
  .content__mluv__center {
    margin-left: 12px;
  }
  .content__mluv__center__text1 {
    font-family: Poppins, Poppins;
    font-weight: 600;
    font-size: deviceValue($device, 36px, 16px);
    color: #eaa30b;
  }
  .content__mluv__center__text2 {
    font-family: Poppins, Poppins;
    font-weight: 400;
    font-size: deviceValue($device, 30px, 12px);
    color: #eaa30b;
  }
  .content__mluv__center__image {
    margin-top: 8px;
    width: deviceValue($device, 192px, 85.33px);
    height: deviceValue($device, 36px, 16px);
    img {
      width: 100%;
      height: 100%;
    }
  }
}

 // 介绍区域
@mixin VARCSS_content__introduce($device: "pc") {
  width: deviceValue($device, 826px, auto);
  .content__introduce__title {
    text-align: deviceValue($device, left, center);
    // padding-left: deviceValue($device, 0px, 24px); // 手机端才增加的
    // padding-right: deviceValue($device, 0px, 24px);// 手机端才增加的
    margin-top: deviceValue($device, 36px, 18px); // 

    font-family: Poppins, Poppins;
    font-weight: 600;
    font-size: deviceValue($device, 54px, 16px);
    color: #181e2a;
  }
  .content__introduce__content {
    text-align: deviceValue($device, left, center);
    margin-top: deviceValue($device, 12px, 6px);
    // padding: 0 538px;

    // width: 844px;
    font-family: Poppins, Poppins;
    font-weight: 400;
    font-size: deviceValue($device, 24px, 11px);
    color: #181e2a;
  }
  .content__introduce__downloads {
    width: deviceValue($device, 100%, 100%);
    display: flex;
    justify-content: deviceValue($device, flex-start, center);
    margin-top: deviceValue($device, 90px, 30px);
    column-gap: deviceValue($device, 16px, 16px);

    //
  }
  .content__introduce__downloads__item {
    font-family: Poppins-Medium, Poppins-Medium;

    min-width: deviceValue($device, 200px, 132px);
    background: #181e2a;
    border-radius: 12px 12px 12px 12px;
  }
  .content__introduce__downloads__item__left {
    margin-top: deviceValue($device, 18px, 10px);
    margin-bottom: deviceValue($device, 18px, 10px);
    margin-left: 12px;

    width: deviceValue($device, 48px, 30px);;
    height: deviceValue($device, 48px, 30px);;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .content__introduce__downloads__item__right {
    margin-left: deviceValue($device, 16px, 6px);
    margin-right: deviceValue($device, 13px, 12px);

    .content__introduce__downloads__item__right__text1 {
      text-align: left;
      // font-family: Poppins, Poppins;
      font-weight: 400;
      font-size: deviceValue($device, 15px, 11px);
      color: #ffffff;
    }
    .content__introduce__downloads__item__right__text2 {
      // font-family: Poppins, Poppins;
      font-weight: 500;
      font-size: deviceValue($device, 24px, 14px);
      color: #ffffff;
    }
  }
}

// 产品内容展示
@mixin VARCSS_content__productshow($device: "pc") {
  padding: deviceValue($device, 0 392px, 0 16px);
  margin-top: deviceValue($device, 90px, 36px); //  这里的pc应该是166，但是现在看着90挺好的，所以有问题的话到时候在说

  .content__productshow__image {
    width: 100%;
    height: 100%;
  }
}

// 文章展示
@mixin VARCSS_content__article($device: "pc") {
  padding: deviceValue($device, 0 310px, 0 16px);

  .content__article__title {
    margin-top: deviceValue($device, 166px, 48px);
    margin-bottom: deviceValue($device, 48px, 12px);

    font-family: Poppins, Poppins;
    font-weight: 600;
    font-size: deviceValue($device, 48px, 16px);
    color: #181e2a;
  }
  .content__article__content {
    column-gap: deviceValue($device, 24px, 12px); // 左右
    row-gap: deviceValue($device, 24px, 12px); // 上下
  }
  .content__article__content__item {
    position: relative; // 确保伪元素相对于父元素定位
    padding: deviceValue($device, 24px, 6px);
    width: deviceValue($device, 401px, 160px);
    // height: deviceValue($device, 601px, 249px); // 去掉了，让他自适应
    border-radius: deviceValue($device, 48px, 18px);
    overflow: hidden; // 确保伪元素不会超出边界

    &::before {
      @include common-before-style($device);
    }

    &:hover::before {
      @include common-after-style(deviceValue($device, 6px, 3px,));
    }
  }
  .content__article__content__item__image {
    width: deviceValue($device, 353.33px, 151.5px);
    height: deviceValue($device, 471.11px, 202px);
  }
  .content__article__content__item__content {
    margin-top: deviceValue($device, 24px, 6px);
    align-items: center;
  }
  .content__article__content__item__title {
    font-family: Barlow Condensed, Barlow Condensed;
    font-weight: 300;
    font-size: deviceValue($device, 48px, 24px);
    color: #181e2a;
  }
  .content__article__content__item__arrow {
    width: deviceValue($device, 48px, 24px);
    height: deviceValue($device, 48px, 24px);
  }
}

// 社交媒体
@mixin VARCSS_content__socialmedia($device: "pc") {
    padding: deviceValue($device, 0 310px, 0 16px);

    .content__socialmedia__title {
      margin-top: deviceValue($device, 167px, 48px);
      margin-bottom: deviceValue($device, 48px, 12px);

      font-family: Poppins, Poppins;
      font-weight: 600;
      font-size: deviceValue($device, 48px, 16px);
      color: #181e2a;
    }
    .content__socialmedia__content {
      column-gap: deviceValue($device, 24px, 12px); // 左右
      row-gap: deviceValue($device, 24px, 12px); // 上下
    }
    .content__socialmedia__content__item {
      position: relative; // 确保伪元素相对于父元素定位
      width: deviceValue($device, 289px, 76px);
      // height: 218px;
      padding: deviceValue($device, 48px 0, 12px 0);

      &::before {
        @include common-before-style($device);
      }

      &:hover::before {
        @include common-after-style(deviceValue($device, 6px, 3px,));
      }
      &:hover .content__socialmedia__content__item__text {
        font-weight: 800; // 悬停时加粗字体
      }
    }
    .content__socialmedia__content__item__image {
      width: deviceValue($device, 96px, 24px);
      height: deviceValue($device, 96px, 24px);
    }
    .content__socialmedia__content__item__text {
      margin-top: deviceValue($device, 24px, 6px);

      font-family: Poppins, Poppins;
      // font-weight: 500;
      font-size: deviceValue($device, 30px, 8px);
      color: #000000;
    }
}


// 团队介绍
@mixin VARCSS_content__team($device: "pc") {

  padding: deviceValue($device, 0 310px, 0 16px);
  .content__team__title {
    margin-top: deviceValue($device, 166px, 50px);
    margin-bottom: deviceValue($device, 48px, 12px);

    font-family: Poppins, Poppins;
    font-weight: 600;
    font-size: deviceValue($device, 48px, 16px);
    color: #181e2a;
  }
  .content__team__content {
    // width: 1298px;
    font-family: Poppins, Poppins;
    font-weight: 400;
    font-size: deviceValue($device, 24px, 11px);
    color: #181e2a;
    line-height: deviceValue($device, 28px, 13px);
    text-align: deviceValue($device, left, center);

  }
  .content__team__content--overflow {
    position: relative;
    max-height:  102px;
    overflow:  hidden;
  }
  .content__team__content--overflow::after { // 隐藏一些内容
    position: absolute;
    bottom: 0;
    left: 0;
    content: "";
    display: block;
    width: 100%;
    // height: 80px;
    height: 100%;
    background: linear-gradient( 180deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.8) 50%, #FFFFFF 100%);
  }
}


@mixin VARCSS_mico-footer($device: "pc") {
  background: url("../assets/img/底部背景@2x.png") left top;
  background-size: 100% 100%;
  height: deviceValue($device, 382px, 90px);

  .mico-footer__email {
    margin-top: deviceValue($device, 166px, 40px);

    font-family: Poppins, Poppins;
    font-weight: 400;
    // font-size: deviceValue($device, 36px, 12px);
    font-size: deviceValue($device, 36px, 10px);
    color: #000000;
    line-height: 42px, 14px;
  }
  .footer__email__image {
    margin-left: deviceValue($device, 24px, 6px);

    width: deviceValue($device, 60px, 24px);
    height: deviceValue($device, 60px, 24px);
  }
  .mico-footer__copyright {
    margin-top: deviceValue($device, 24px, 9px);
    column-gap: deviceValue($device, 48px, 13px);

    font-family: Poppins, Poppins;
    font-weight: 400;
    font-size: deviceValue($device, 24px, 8px);
    color: #181e2a;
    line-height: deviceValue($device, 28px, 9px);
  }
  .mico-footer__copyright__item {
  }
}

.toast {
  position: fixed;
  top: 70%;
  left: 50%;
  transform: translate(-70%, -50%) scale(0.9);
  background-color: #fff;
  color: #333;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.1);
  text-align: center;
  // max-width: 80%;
  box-sizing: border-box;
  opacity: 0;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.toast--visible {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}