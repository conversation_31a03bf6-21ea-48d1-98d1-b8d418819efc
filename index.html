<!DOCTYPE html>
<html lang="en">
<head>
    <!-- 设置网页字符编码为UTF-8 -->
    <meta charset="UTF-8">
    <!-- 设置视口,使页面在移动设备上正常显示 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 增加标题 -->
    <title>Colorful widget 彩虹组件官网-手机桌面美化App</title>
    <!-- 增加描述 -->
    <meta name="description" content="Colorful Widget是一款万能桌面主题壁纸图标美化APP，支持IOS和安卓双端；提供上万种桌面风格，各种高清动态壁纸、创意小组件、精美主题图标、灵动岛动画等；轻松打造个性化桌面，提升手机实用性和趣味性！">
    <!-- 增加关键词 -->
    <meta name="keywords" content="万能小组件,手机壁纸,灵动岛,小组件,图标,锁屏,主题壁纸,主题商店,墙纸,iscreen,灵动小组件">
    <!-- 百度站长平台 -->
    <meta name="baidu-site-verification" content="codeva-YAaNwzWhLE" />
    <!-- 友盟 u-link -->
    <script src="https://g.alicdn.com/jssdk/u-link/index.min.js"></script>
    <!-- 数数 -->
     <script src="./dependencies/ta_js_sdk/thinkingdata.umd.min.js"></script>
     <!-- <script src="/colorfulwidget_official_website/dependencies/ta_js_sdk/thinkingdata.umd.min.js"></script> -->
     <!-- vconsole 引入 -->
     <!-- <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vConsole/3.11.0/vconsole.min.js"></script> -->
    <!-- 引入 tailwindcss -->
    <!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css"> -->
    <!-- 引入 sass -->
    <!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sass.js@0.13.3/dist/sass.min.js"> -->

    <!-- icon -->
    <link rel="icon" href="./assets/images/logo.png">
    <style>
        * {
            /* 让 padding margin 占自己 */
            box-sizing: border-box;
            /* box-sizing: content-box; */
            margin: 0;
            padding: 0;
        }
        .cursor-pointer {
            cursor: pointer;
        }
        .website {
            background-color: #fff;
            /* padding-top: 40px; */
            padding-top: 30px;
            padding-left: 110px;
            padding-right: 110px;
            padding-bottom: 10px;
            
            width: 100%;
            min-height: 100vh;
        }
        /* 移动端样式 */
        @media screen and (max-width: 768px) {
            .website {
                padding-top: 20px;
                padding-left: 12px;
                padding-right: 12px;
                padding-bottom: 10px;
            }
        }
        .website__top {
            /* row-gap: 100px; */
            /* column-gap: 160px; */
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }
        @media screen and (max-width: 768px) {
            .website__top {
                flex-direction: column;
            }
        }
        /* logo 下载 区域 */
        .website__widget {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .website__logos {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 500px;   
        }
        @media screen and (max-width: 768px) {
            .website__logos {
                width: auto;   
            }
        }

        .website__logos__img {
            width: 100px;
            height: 100px;
        }

        .website__logos__text {
            margin-top: 20px;
            font-size: 32px;
            font-weight: bold;
        }
        @media screen and (max-width: 768px) {
            .website__logos__text {
                font-size: 24px;
            }
        }


        .website__logos__desc {
            margin-top: 20px;
            font-size: 20px;
            color: #555;
            letter-spacing: 2px;
        }
        @media screen and (max-width: 768px) {
            .website__logos__desc {
                font-size: calc(20px - 8px);
            }
        }

        .website__downloads__app {
            margin-top: 30px;

            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
        }

        @media screen and (max-width: 768px) {
            .website__downloads__app {
                margin-top: calc(60px / 2);;
            }
        }

        .downloads__app__android {
            display: flex;
            flex-direction: row;
            column-gap: 10px;
        }
        .downloads__app__android img {
            flex: 1;
            /* width: 170px; */
            height: 50px;
        }
        .downloads__app__qrcode__img {
            margin-left: 20px;
            position: relative;
            display: flex;
            flex-direction: row;
        }
        @media screen and (max-width: 768px) {
            .downloads__app__qrcode__img {
                display: none;
            }
        }
        /* 添加悬浮效果 */
        .downloads__app__qrcode__img:hover > .downloads__app__qrcode__img2{
            display: block;
        }
        .downloads__app__qrcode__img1 {
            width: 50px;
            height: 50px;
        }
        .downloads__app__qrcode__img2 {
            background: url('./assets/images/code-big.png') no-repeat center center;
            background-size: 100% 100%;
            height: 154px;
            left: -60px;
            position: absolute;
            top: calc(50px + 0px);
            width: 200px;
            height: 200px;
            z-index: 10;
            display: none;
        }

        /* 图片展示 */
        .website__imageshow {
            display: flex;
            flex-direction: row;
            /* background-color: red; */
            background: url('./assets/images/官网.png') no-repeat center center;
            background-size: contain;
            height: 650px;
            width: 1200px;
        }
        @media screen and (max-width: 768px) {
            .website__imageshow {
                width: 100%;
                height: calc(500px / 2);
            }
        }

        /* 关注相关 */
        .website__follow {
            margin-top: 0px;

            display: flex;
            flex-direction: column;
        }
        @media screen and (max-width: 768px) {
            .website__follow {
                margin-top: 20px;
                align-items: center;
            }
        }
        
        .website__follow__text {
            margin-top: 4px;
            
            font-size: 16px;
            color: #555;
            letter-spacing: 2px;
        }
        .website__follow__text__colorful {
        }
        @media screen and (max-width: 768px) {
            .website__follow__text {
                font-size: 14px;
            }
            .website__follow__text__colorful {
                text-align: center;
            }
        }


        .website__follow__imgs {
            margin-top: 4px;
            column-gap: 10px;
            display: flex;
            flex-direction: row;
        }
        .website__follow__imgs img {
            width: 30px;
            height: 30px;
        }


        /* 底部备案信息 */
        .website__footer {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            
            margin-top: 20px;
            color: #666;
            letter-spacing: 2px;
        }
        @media screen and (max-width: 768px) {
            .website__footer {
                margin-top: 60px;
            }
        }


        .website__footer__t1 {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            
            font-size: 14px;
        }
        @media screen and (max-width: 768px) {
            .website__footer__t1 {
                font-size: 12px;
            }
        }


        .website__footer__t2 {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: center;

            margin-top: 4px;
            font-size: calc(12px + 6px);
        }
        @media screen and (max-width: 768px) {
            .website__footer__t2 {
                font-size: calc(12px + 0px);
            }
        }
        .mx-1 {
            margin-left: 4px;
            margin-right: 4px;
        }
    </style>
</head>
<body>
    <div class="website w-full min-h-screen">
        <div class="website__top">
            <!-- logo 下载 区域 -->
            <div class="website__widget">
                <!-- logo 相关 -->
                <div class="website__logos">
                    <!-- logo图 -->
                    <img class="website__logos__img" src="./assets/images/logo.png">
                    <!-- 标题 -->
                    <div class="website__logos__text">Colorful Widget 彩虹组件</div>
                    <!-- 描述 -->
                    <div class="website__logos__desc">
                        <div>小组件/主题/ 高清壁纸/ 精美图标/灵动岛宠物...</div>
                        <div>数百种风格自由搭配，轻松打造个性化手机桌面！</div>
                    </div>
                </div>
                <!-- APP 下载按钮 和 扫码下载 -->
                <div class="website__downloads__app">
                    <!-- android ios 下载 -->
                    <div class="downloads__app__android">
                        <img class="cursor-pointer downloads__app__ios__img" src="./assets/images/ios下载.png">
                        <img class="cursor-pointer downloads__app__android__img" id="androidDownloadId" src="./assets/images/安卓下载.png"> 
                    </div>
                    <!-- 二维码 （pc的时才展示） -->
                    <div class="downloads__app__qrcode__img ">
                        <!-- 二维码 -->
                        <img class="downloads__app__qrcode__img1" src="./assets/images/code-small.jpg">
                        <!-- 悬浮上面的二维码后展示的图片 -->
                         <div class="downloads__app__qrcode__img2"></div>
                    </div>
                </div>
            </div>

            <!-- 图片展示 -->
            <div class="website__imageshow" >
            </div>
        </div>


        <!-- 关注相关 -->
        <div class="website__follow">
            <div class="website__follow__text">
                更多精彩内容，请关注官方账户:
                <div class="website__follow__text__colorful">Colorful Widget</div>
            </div>
            <div class="website__follow__imgs">
                <img src="./assets/images/小红书.png">
                <img src="./assets/images/抖音.png">
                <img src="./assets/images/快手.png">
            </div>
        </div>

        <!-- 底部备案信息 -->
         <div class="website__footer">
            <div class="website__footer__t1">
                <div>联系邮箱：<EMAIL> </div>
            </div>
            <div class="website__footer__t1">
                <div class="website__footer__user__agreement cursor-pointer">用户协议</div> 
                <span class="mx-1">|</span>
                <div class="website__footer__privacy__agreement cursor-pointer">隐私协议</div>
            </div>
            <!-- <div class="website__footer__t1">
            </div> -->
            <div class="website__footer__t2">
                广州至锋信息技术有限公司
                <span class="mx-1">|</span>
                粤ICP备2020078974号-6
            </div>
         </div>
    </div>
</body>
<script>
let LinkResults = {
    AndroidLink: 'https://apkresource.own.unbing.cn/apk%E6%9B%B4%E6%96%B0/ColorfulWidget_release_1117_others.apk',
    IOSLink: 'https://colorfulwidget.onelink.me/ta2s/zm0ziyek',
    // 隐私协议跳转
    PrivacyAgreementLink: 'http://global.download.sharpmobi.com/resources/com.sm.widget/html/PrivacyPolicy.html',
    // 用户协议跳转
    UserAgreementLink: 'http://global.download.sharpmobi.com/resources/com.sm.widget/html/TermsOfUse.html',
}
let elements = {
    android: document.querySelector('.downloads__app__android__img'),
    ios: document.querySelector('.downloads__app__ios__img'),
    userAgreement: document.querySelector('.website__footer__user__agreement'),
    privacyAgreement: document.querySelector('.website__footer__privacy__agreement'),
}
// 其他渠道唤起app
const otherChannel = () => {
     // 尝试直接拉起app
     const iframe = document.createElement('iframe');
    iframe.style.display = 'none';
    iframe.src = "colorful://com.colorful.widget/colorful";
    document.body.appendChild(iframe);
    
    // 2秒后检查是否成功唤起
    setTimeout(() => {
        window.location.href = LinkResults.AndroidLink;
    }, 500);
}
// 微信渠道唤起app
const wechatChannel = () => {
    ULink([{
        id: "usr1c75rk927quf6",// 后台生成的裂变活动LinkID
        data: {// 传递的自定义动态参数
        },
        selector: "",
        // 可选高级功能，具体含义请看下方U-Link API文档
        auto: true, // 则H5页面打开加载时会尝试唤起App。
        timeout: 1000,
        lazy: false,
    }])
}
elements.android.addEventListener('click', () => {
    window.ta.track('h5_download_click', {})

    setTimeout(() => {
        if (ULink.isWechat) {
            wechatChannel();
        } else {
            otherChannel();
        }
    }, 300);
})
elements.ios.addEventListener('click', () => {
    window.location.href = LinkResults.IOSLink;
})
elements.userAgreement.addEventListener('click', () => {
    window.open(LinkResults.UserAgreementLink, '_blank');
})
elements.privacyAgreement.addEventListener('click', () => {
    window.open(LinkResults.PrivacyAgreementLink, '_blank');
})

const thinkingInit = () => {
    // 创建 SDK 配置对象
    var config = {
        appId: '33936ff305c94ce8a212e50b883e93dc',
        serverUrl: 'https://tacollect.sharpmobi.com',
        batch: false,//数据先缓存到本地，然后批量发送，默认为false
        autoTrack: {
            pageShow: true, //开启页面展示事件，事件名ta_page_show
            pageHide: true, //开启页面隐藏事件，事件名ta_page_hide
        }
    };
    // 将 SDK 实例赋给全局变量 ta，或者其他您指定的变量
    console.log("🚀 ~ thinkingInit ~ thinkingdata:", thinkingdata)
    window.ta = thinkingdata;
    // 用配置对象初始化 SDK
    ta.init(config);

    let test_type = '测试用户'
    if (window.location.host.indexOf('colorfulwidget.com') === -1) {
        test_type = '测试用户'
    } else {
        test_type = '正式用户'
    }
    let data = {
      'test_type': test_type,
    }
    //设置公共事件属性
    thinkingdata.setSuperProperties(data)
}

const __pageInit = () => {
      // 302 圣诞 view-source:https://webcdn.colorfulwidget.com/colorfulwidget/christmas/jump_umeng_dl.html
    // https://share.umeng.com/demo/ulink/index.html
    // demo https://gitee.com/umengplus/sharelink-jssdk-demo/tree/main#tracker%E8%BF%BD%E8%B8%AA%E5%AF%B9%E8%B1%A1
    // 友盟 基础介绍 https://developer.umeng.com/docs/191212/detail/212402
    // 友盟 创建裂变活动方式 https://developer.umeng.com/docs/191212/detail/196072 
    // js sdk接入 https://developer.umeng.com/docs/191212/detail/193297
    // 事件 https://developer.umeng.com/docs/191212/detail/200978
    
    thinkingInit();

    // // 创建 vConsole 实例
    // const vConsole = new VConsole();
    // vConsole.init();
}


window.addEventListener('load', () => {
    try {
        // 初始化
        __pageInit();
    } catch(err) {
        console.error('初始化失败:', err);
    }
});
</script>
</html>